from bs4 import BeautifulSoup
import re
from datetime import timedelta
from bs4.element import Tag


def parse_flight_details(html_list):

    if not html_list or not isinstance(html_list, list):
        return {"error": "No HTML data provided"}
    soup = BeautifulSoup(html_list[0], "html.parser")
    # --- Parse Segments (Legs) ---
    segments = []
    total_duration_minutes = 0
    all_terminals = set()
    intermediate_airports = []
    date = None
    trip_type = None
    stops = "Direct"

    # Find all segment accordions
    for seg_header in soup.select(".accordion-header.ng-star-inserted"):
        seg = {
            "from": {"code": None, "city": None, "terminal": None},
            "to": {"code": None, "city": None, "terminal": None},
            "flight_name": None,
            "aircraft": None,
            "times": {"departure": None, "arrival": None, "duration": None},
        }
        # Codes and cities
        places = (
            seg_header.select_one(".accordion-header-head-places")
            if isinstance(seg_header, Tag)
            else None
        )
        if places:
            spans = places.find_all("span")
            if len(spans) >= 2:
                seg["from"]["code"] = spans[0].get_text(strip=True)
                seg["to"]["code"] = spans[1].get_text(strip=True)
        # Flight name
        number = (
            seg_header.select_one(".accordion-header-head-number")
            if isinstance(seg_header, Tag)
            else None
        )
        if number:
            seg["flight_name"] = number.get_text(strip=True)
        # Date
        date_div = (
            seg_header.select_one(".accordion-header-head-date span")
            if isinstance(seg_header, Tag)
            else None
        )
        if date_div:
            date = date_div.get_text(strip=True).split(",")[-1].strip()
        # Times and duration
        # Try to get from the corresponding .flight-leg-info section
        seg_box = None
        parent_accordion = (
            seg_header.find_parent("div", class_="idp-summary__accordion")
            if isinstance(seg_header, Tag)
            else None
        )
        if parent_accordion:
            seg_box = parent_accordion.find_next("div", class_="idp-summary__box-left")
        if seg_box and isinstance(seg_box, Tag):
            leg_info = (
                seg_box.select_one(".flight-leg-info-content-desc")
                if isinstance(seg_box, Tag)
                else None
            )
            if leg_info and isinstance(leg_info, Tag):
                descs = [d for d in leg_info.find_all(["div"]) if isinstance(d, Tag)]
                # SFO, San Francisco, Terminal, Duration, LAX, Los Angeles, Terminal
                codes = [
                    d.get_text(strip=True)
                    for d in descs
                    if d.has_attr("class")
                    and "flight-leg-info-content-desc-code" in d["class"]
                ]
                cities = [
                    d.get_text(strip=True)
                    for d in descs
                    if d.has_attr("class")
                    and "flight-leg-info-content-desc-place" in d["class"]
                ]
                terminals = [
                    d.get_text(strip=True)
                    for d in descs
                    if d.has_attr("class")
                    and "flight-leg-info-content-desc-terminal" in d["class"]
                ]
                times = [
                    d.get_text(strip=True)
                    for d in descs
                    if d.has_attr("class")
                    and "flight-leg-info-content-desc-time" in d["class"]
                ]
                if codes:
                    seg["from"]["code"] = codes[0]
                    seg["to"]["code"] = codes[-1]
                if cities:
                    seg["from"]["city"] = cities[0]
                    seg["to"]["city"] = cities[-1]
                if terminals:
                    seg["from"]["terminal"] = terminals[0]
                    seg["to"]["terminal"] = terminals[-1]
                    all_terminals.update(terminals)
                if times:
                    seg["times"]["duration"] = times[0]
            # Departure/arrival times from headers
            header = (
                seg_box.find_parent("div", class_="idp-summary__accordion-content")
                if isinstance(seg_box, Tag)
                else None
            )
            if header and isinstance(header, Tag):
                leg_section = header.select_one(".flight-leg-info")
                if leg_section and isinstance(leg_section, Tag):
                    dep = leg_section.select_one(".flight-leg-info-header")
                    arr = leg_section.select_one(".flight-leg-info-footer")
                    if dep:
                        seg["times"]["departure"] = dep.get_text(strip=True)
                    if arr:
                        seg["times"]["arrival"] = arr.get_text(strip=True)
        # Aircraft
        aircraft = None
        if seg_box and isinstance(seg_box, Tag):
            aircraft = seg_box.find_next("div", class_="idp-summary__box-fleet")
        if aircraft:
            seg["aircraft"] = aircraft.get_text(strip=True)
        # Duration in minutes
        dur_str = seg["times"].get("duration")
        if dur_str:
            m = re.match(r"(?:(\d+)h)?\s*(\d+)m", dur_str)
            if m:
                h = int(m.group(1)) if m.group(1) else 0
                m_ = int(m.group(2))
                total_duration_minutes += h * 60 + m_
        segments.append(seg)

    # --- Parse Layovers/Intermediate Airports ---
    for layover in soup.select(".accordion-layover-info.ng-star-inserted"):
        desc = (
            layover.select_one(".accordion-layover-info-desc")
            if isinstance(layover, Tag)
            else None
        )
        city = None
        code = None
        terminal = None
        if desc and isinstance(desc, Tag):
            text = desc.get_text(strip=True)
            city_match = re.search(r"([A-Za-z ]+),", text)
            code_match = re.search(r"in ([A-Z]{3})", text)
            if city_match:
                city = city_match.group(1).strip()
            if code_match:
                code = code_match.group(1)
        if segments:
            terminal = segments[-1]["to"].get("terminal")
        intermediate_airports.append({"code": code, "city": city, "terminal": terminal})
    if intermediate_airports:
        stops = f"{len(intermediate_airports)} Stop{'s' if len(intermediate_airports) > 1 else ''}"
    else:
        stops = "Direct"

    # --- Parse Cabin Classes (from all htmls) ---
    benefits_map = {
        "icon-wif": "Wi-Fi",
        "icon-usb": "USB Power",
        "icon-110": "110V AC Power",
        "icon-studio": "Delta Studio",
        "icon-entertain": "Personal Entertainment",
        "icon-satellite": "Live Satellite TV",
        "icon-sky": "Sky Priority",
    }
    cabin_classes = []
    for html in html_list:
        if not html or not isinstance(html, str):
            continue
        soup = BeautifulSoup(html, "html.parser")
        for tab in soup.select(".brand-tabs__info"):
            cabin_data = {}
            name_div = (
                tab.select_one(".brand-tabs__info-name")
                if isinstance(tab, Tag)
                else None
            )
            if name_div:
                cabin_data["class"] = name_div.get_text(strip=True)
            points_span = (
                tab.select_one(".brand-tabs__info-mile")
                if isinstance(tab, Tag)
                else None
            )
            if points_span:
                points = points_span.get_text(strip=True).replace(",", "")
                cabin_data["points"] = points
            dollar_span = (
                tab.select_one(".brand-tabs__info-mile-price")
                if isinstance(tab, Tag)
                else None
            )
            if dollar_span:
                amount = dollar_span.get_text(strip=True)
                cabin_data["Extra_amount_required"] = f"{amount} USD"
            # Amenities
            benefits = set()
            for icon in soup.select(".accordion-amenities-icon"):
                if not isinstance(icon, Tag):
                    continue
                title = icon.get("title")
                if title and isinstance(title, str):
                    benefits.add(title.split(" - ")[0])
                classes = icon.get("class")
                if classes and isinstance(classes, list):
                    for c in classes:
                        if c in benefits_map:
                            benefits.add(benefits_map[c])
            cabin_data["benefits"] = sorted(list(benefits))
            # Meal Services
            meal_services = []
            for meal in soup.select(".accordion-meal-cabin span"):
                if not isinstance(meal, Tag):
                    continue
                text = meal.get_text(strip=True)
                if text and text != "," and not text.startswith("&nbsp;"):
                    meal_services.append(text)
            cabin_data["meal_services"] = meal_services
            if cabin_data:
                cabin_classes.append(cabin_data)
    # Remove duplicates
    seen = set()
    unique_cabins = []
    for c in cabin_classes:
        key = (c.get("class"), c.get("points"), c.get("Extra_amount_required"))
        if key not in seen:
            seen.add(key)
            unique_cabins.append(c)

    # --- Trip Type ---
    price_section = soup.select_one(".flight-card__details-trip")
    if price_section:
        trip_type = price_section.get_text(strip=True)
    else:
        trip_type = "One-Way"

    # --- Compose Final Data ---
    data = {
        "date": date,
        "segments": segments,
        "flight_name": ", ".join(
            [s["flight_name"] for s in segments if s["flight_name"]]
        ),
        "aircraft": ", ".join([s["aircraft"] for s in segments if s["aircraft"]]),
        "times": {
            "departure": (
                segments[0]["times"]["departure"]
                if segments and segments[0]["times"]["departure"]
                else None
            ),
            "arrival": (
                segments[-1]["times"]["arrival"]
                if segments and segments[-1]["times"]["arrival"]
                else None
            ),
            "duration": (
                str(timedelta(minutes=total_duration_minutes))
                if total_duration_minutes
                else None
            ),
        },
        "from": (
            segments[0]["from"]
            if segments
            else {"code": None, "city": None, "terminal": None}
        ),
        "to": (
            segments[-1]["to"]
            if segments
            else {"code": None, "city": None, "terminal": None}
        ),
        "trip_type": trip_type,
        "stops": stops,
        "intermediate_airports": [
            ia for ia in intermediate_airports if any(ia.values())
        ],
        "cabin_classes": unique_cabins,
    }
    return data
