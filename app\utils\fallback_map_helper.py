from app.core.types.exception_types import ExceptionContext


class RegisterFallbackHelper:
    def __init__(self):
        self.fallback_map = {}

    def _register_fallback(
        self, context: ExceptionContext, selector: str, handlerMethod: callable
    ):

        if context not in self.fallback_map:
            self.fallback_map[context] = {}
        self.fallback_map[context][selector] = handlerMethod
