from app.core.scraper_engine import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from playwright.async_api import Page
from app.airline_parsers.delta_airline_parser import parse_flight_details
from app.airline_scrapers.delta_airline.selector_dict import SELECTORS as selectors
from app.core.types.exception_types import ExceptionContext
from app.core.exception.scraper_exception import ScraperException
from app.airline_scrapers.delta_airline.delta_airline_fallback import DeltaFallback


class DeltaScraper(ScraperEngine, DeltaFallback):
    async def _scrape(self, page: Page):

        try:
            # page.set_default_timeout(0)
            await page.goto(self.url)
            await page.screenshot(path="before.png")
            await self.try_click(
                page,
                selectors["accept_cookies"],
            )
            await self.try_click(
                page,
                selectors["select_trip_type_dropdown"],
            )
            if self.trip_type == "one_way":
                await self.try_click(
                    page,
                    selectors["select_one_way_trip"],
                )

            await self.try_click(
                page,
                selectors["click_shop_with_miles"],
            )
            await self.try_click(
                page,
                selectors["select_departure_date"],
            )
            # here the args for the date has to be passed will need a loop to
            # selected date selector based on the current date like at first we start the
            # initial div then collect its content and compare with curretn date , if true then select the
            # current selector and from there on switch to other selctor based on the current indexing
            # need to check the current div postion to decide th month and move forward
            await self.try_click(
                page,
                selectors["select_departure_date_day"],
            )
            await self.try_click(
                page,
                selectors["click_done_button"],
            )
            await self.try_click(
                page,
                selectors["from_airport"],
            )
            await self.try_fill(
                page,
                selectors["search_input"],
                "",
            )  # the args will be dynamic
            await self.try_press_sequential(
                page,
                selectors["search_input"],
                f"{self.from_city}",
                delay=100,
            )
            await self.try_wait_for_timeout(page, 2000)
            await self.try_press_enter(
                page,
                selectors["search_input"],
                "Enter",
            )
            await self.try_click(
                page,
                selectors["to_airport"],
            )
            await self.try_fill(
                page,
                selectors["search_input"],
                "",
            )  # the args will be dynamic
            await self.try_press_sequential(
                page,
                selectors["search_input"],
                f"{self.to_city}",
                delay=100,
            )
            await self.try_wait_for_timeout(page, 2000)
            await self.try_press_enter(
                page,
                selectors["search_input"],
                "Enter",
            )
            await self.try_click(
                page,
                selectors["initial_submit_button"],
            )
            await self.try_wait_for_load_state(page, "domcontentloaded")
            await self.try_wait_for_timeout(page, 10000)
            await self.try_click(
                page,
                selectors["flexible_date_result_page"],
            )
            await self.try_wait_for_timeout(page, 10000)
            await self.try_screenshot(page, "test1.png")

            count = 0
            flight_details_dom: list = []
            while True:
                if await self.try_check_visibility(
                    page,
                    selectors["flight_details"],
                    var0=count,
                ):
                    await self.try_click(
                        page,
                        selectors["flight_details"],
                        var0=count,
                    )
                    await self.try_wait_for(
                        page,
                        selectors["wait_for_flight_details_content"],
                    )
                    cabin_class_list = [
                        "click_main_cabinClass",
                        "click_comfort_cabinClass",
                        "click_first_cabinClass",
                    ]
                    for cabin_class in cabin_class_list:
                        if await self.try_check_visibility(
                            page, selectors[f"{cabin_class}"]
                        ):
                            await self.try_click(
                                page,
                                selectors[f"{cabin_class}"],
                            )
                            await self.try_wait_for(
                                page,
                                selectors["wait_for_flight_details_content"],
                            )
                            flight_details_dom.append(
                                await self.get_innerHtml(
                                    page,
                                    selectors["flight_details_dom_selector"],
                                )
                            )
                    await self.try_click(
                        page,
                        selectors["details_close_button"],
                    )
                    # await self.try_wait_for(page, selectors["seat_details"])
                    await self.try_click(
                        page,
                        selectors["seat_details"],
                        var0=count,
                    )
                    await self.try_wait_for(
                        page, selectors["seat_details_load"], timeout=5000
                    )
                    await self.try_scroll_to_page_bottom(page)
                    seat_details = await self.get_page_content(page)
                    await self.try_click(
                        page,
                        selectors["seat_details_close_button"],
                    )
                    count += 1
                    parsed_json = parse_flight_details(flight_details_dom)
                    print(parsed_json)
                elif await self.try_check_visibility(
                    page,
                    selectors["see_more_result_button"],
                ):
                    await self.try_click(
                        page,
                        selectors["see_more_result_button"],
                    )
                    await self.try_wait_for(
                        page,
                        selectors["flight_details"],
                        var0=count + 1,
                    )
                    continue
                else:
                    break

                #         except Exception as e:
                #             print(f"Error in details: {e}")
                #             break
                #         count+=1
                #         if not await page.locator(f'//*[@id="#details-link{count}"]').is_visible():
                #             break
                # moreResult = page.get_by_text(" See More Results ")
                # while await moreResult.is_visible():
                #     await moreResult.evaluate(
                #         "el => el.scrollIntoView({ behavior: 'smooth', block: 'center' })"
                #     )
                # await moreResult.click()
                # await page.wait_for_timeout(1000)
                # moreResult = page.get_by_text(" See More Results ")
            await page.screenshot(path="afterscroll.png")
            await page.goto("https://www.delta.com/flightsearch/book-a-flight")
        except Exception as e:
            print(e)

            if isinstance(e, ScraperException):
                print(e)
                await self.delta_fallback_handler(page, e.context, e.failed_selector)
            else:
                self.exception_logger(
                    error=f"Caught some exception: {type(e).__name__} - {e}",
                    context=ExceptionContext.DELTA_SCRAPER_FATAL_ERROR,
                )

        # except ScraperException as e:
        #     print(e)
        #     await self.delta_fallback_handler(page, e.context)

        await page.wait_for_timeout(2000)
        print("Delta scraping done.")
