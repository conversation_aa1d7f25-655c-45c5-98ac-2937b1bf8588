import traceback

from app.core.exception.scraper_exception import ScraperException
from app.core.logging.scrape_error import scraper_logger
from app.core.types.exception_types import ExceptionContext


class ExceptionLogger:
    def exception_logger(
        self,
        error: str,
        context: ExceptionContext = ExceptionContext.VALIDATION_ERROR,
        failed_selector: str = "",
    ):

        full_message = f"[{context}] {str(error)}\n{traceback.format_exc()}"
        scraper_logger.error(full_message)

        #     self.send_telegram_alert(full_message)
        return ScraperException(
            message=error, context=context, failed_selector=failed_selector
        )

    # def send_telegram_alert(self, message: str,context:ExceptionContext):
    #     # Example: Replace with your actual bot token and chat ID
    #     BOT_TOKEN = "123456789:ABC-your-bot-token"
    #     CHAT_ID = "987654321"
    #     telegram_url = (
    #         f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    #         f"?chat_id={CHAT_ID}&context={context}&text={message}"
    #     )
    #     # requests.get(telegram_url)  # Uncomment to send
    #     print(f"[Telegram Alert] {message}")
