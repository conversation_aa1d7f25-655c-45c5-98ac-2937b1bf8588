from playwright.async_api import Page

async def DeltaScraper(page: Page):
    try:
        await page.locator("#onetrust-close-btn-container").click()
        await page.locator("#selectTripType-val").click(timeout=1000)
        await page.locator("#ui-list-selectTripType1").click()
        await page.locator('#shopWithMiles').click(force=True)

        await page.locator('#input_departureDate_1').click(timeout=3000)
        await page.locator(f'[aria-label="04 July 2025, Friday"][class="dl-state-default"]',timeout=2000)
        await page.locator(f'[aria-label="done"]').click()

        for x in range(10):
            try:
                await page.locator('#fromAirportName').click(force=True)
                await page.locator("#search_input").fill("")
                searchInput = page.locator("#search_input")
                await searchInput.press_sequentially("LAX", delay=100)
                await page.wait_for_timeout(2000)
                await searchInput.press("Enter")

                await page.locator('#toAirportName').click(force=True)
                await page.locator("#search_input").fill("")
                searchInput = page.locator("#search_input")
                await searchInput.press_sequentially("LGA", delay=100)
                await page.wait_for_timeout(2000)
                await searchInput.press("Enter")

                await page.locator("#btnSubmit").click()

                await page.wait_for_load_state("domcontentloaded")
                await page.wait_for_timeout(10000)

                try:
                    await page.locator('[class="idp-btn idp-primary idp-btn-large + \' ng-star-inserted"]').click(timeout=2000)
                except Exception as e:
                    print(f"Button click failed: {e}")

                await page.wait_for_timeout(10000)
                await page.screenshot(path=f"partialbeforeSearch_{x}.png")

                moreResult = page.get_by_text(" See More Results ")
                while await moreResult.is_visible():
                    await moreResult.evaluate("el => el.scrollIntoView({ behavior: 'smooth', block: 'center' })")
                    await moreResult.click()
                    await page.wait_for_timeout(1000)
                    moreResult = page.get_by_text(" See More Results ")

                await page.goto("https://www.delta.com/flightsearch/book-a-flight")

            except Exception as loop_err:
                print(f"Error during loop iteration {x}: {loop_err}")

        await page.screenshot(path="test.png")

        await page.locator('#fromAirportName').click(force=True)
        await page.locator("#search_input").fill("")
        searchInput = page.locator("#search_input")
        await searchInput.press_sequentially("LAX")
        await page.wait_for_timeout(2000)
        await searchInput.press("Enter")

    except Exception as e:
        print(f"Error in DeltaScraper: {e}")
