from playwright.async_api import Page
from app.core.exception.scraper_exception import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from app.core.types.selector_types import Selector<PERSON>air
from app.core.exception.exception_logger import ExceptionLogger
from app.core.types.exception_types import ExceptionContext
from app.core.utils.selector_type_util import NamedSelector


class Fallback(ExceptionLogger):

    def get_dynamic_selector(
        self, named_selector: NamedSelector, var: dict
    ) -> NamedSelector:
        name = named_selector.name
        xpath = None
        css = None
        # args={}
        # for i in range(len(var)):
        #     args=var.copy()
        if named_selector.selector.xpath:
            try:
                xpath = named_selector.selector.xpath.format(**var)
            except Exception as e:
                self.exception_logger(
                    error=f" selector error [XPATH] failed for selector {named_selector.name} ERROR: {e}",
                    context=ExceptionContext.DYNAMIC_SELECTOR_ERROR,
                    failed_selector=named_selector.name,
                )
        if named_selector.selector.css:
            try:
                css = named_selector.selector.css.format(**var)
            except Exception as e:
                self.exception_logger(
                    error=f" selector error [CSS] failed for selector {named_selector.name} ERROR:{e}",
                    context=ExceptionContext.DYNAMIC_SELECTOR_ERROR,
                    failed_selector=named_selector.name,
                )
        if not xpath and not css:
            raise self.exception_logger(
                error=f"all selectors for {named_selector.name} failed",
                context=ExceptionContext.TRY_CLICK_ERROR,
                failed_selector=named_selector.name,
            )
        return NamedSelector(name, selector=SelectorPair(xpath, css))

    async def try_check_visibility(
        self, page: Page, named_selector: NamedSelector, *args, **kwargs
    ) -> bool:

        var = {k: kwargs.pop(k) for k in list(kwargs) if k.startswith("var")}

        if var:
            named_selector = self.get_dynamic_selector(named_selector, var)
        if named_selector.selector.xpath:
            try:

                return await page.locator(named_selector.selector.xpath).is_visible()
            except Exception as e:
                self.exception_logger(
                    error=f"Selector error [XPATH] failed for selector {named_selector.name} ERROR:{e}",
                    context=ExceptionContext.TRY_CHECK_VISIBILITY_ERROR,
                    failed_selector=named_selector.name,
                )
        if named_selector.selector.css:
            try:

                return await page.locator(named_selector.selector.css).is_visible()
            except Exception as e:
                self.exception_logger(
                    error=f"Selector error [CSS] failed for selector {named_selector.name} ERROR{e}",
                    context=ExceptionContext.TRY_CHECK_VISIBILITY_ERROR,
                    failed_selector=named_selector.name,
                )
        raise self.exception_logger(
            error=f"all selectors for {named_selector.name} failed ",
            context=ExceptionContext.TRY_CHECK_VISIBILITY_ERROR,
            failed_selector=named_selector.name,
        )

    async def try_click(
        self,
        page: Page,
        named_selector: NamedSelector,
        *args,
        **kwargs,
    ):
        """Generic click fallback logic."""
        var = {k: kwargs.pop(k) for k in list(kwargs) if k.startswith("var")}

        if var:
            named_selector = self.get_dynamic_selector(named_selector, var)
        if named_selector.selector.xpath:
            try:
                await page.locator(named_selector.selector.xpath).click(**kwargs)
                return
            except Exception as e:
                # print(f"[XPath failed] {e}")
                self.exception_logger(
                    error=f"Selector error [XPATH] failed for selector {named_selector.name} ERROR:{e}",
                    context=ExceptionContext.TRY_CLICK_ERROR,
                    failed_selector=named_selector.name,
                )
        if named_selector.selector.css:
            try:
                await page.locator(named_selector.selector.css).click()
                return
            except Exception as e:
                self.exception_logger(
                    error=f"Selector error [CSS] failed for selector {named_selector.name} ERROR:{e}",
                    context=ExceptionContext.TRY_CLICK_ERROR,
                    failed_selector=named_selector.name,
                )
        if named_selector.selector.getByText:
            try:
                await page.get_by_text(named_selector.selector.getByText).click()
                return
            except Exception as e:
                self.exception_logger(
                    error=f"Selector error [GETBYTEXT] failed for selector {named_selector.name} ERROR:{e}",
                    context=ExceptionContext.TRY_CLICK_ERROR,
                    failed_selector=named_selector.name,
                )
        raise self.exception_logger(
            error=f"all selectors for {named_selector.name} failed",
            context=ExceptionContext.TRY_CLICK_ERROR,
            failed_selector=named_selector.name,
        )

    async def try_fill(
        self,
        page: Page,
        named_selector: NamedSelector,
        *args,
        selector_name: str = "",
        **kwargs,
    ):
        """Generic fill fallback logic."""
        var = {k: kwargs.pop(k) for k in list(kwargs) if k.startswith("var")}

        if var:
            named_selector = self.get_dynamic_selector(named_selector, var)
        if named_selector.selector.xpath:
            try:
                await page.locator(named_selector.selector.xpath).fill(*args)
                return
            except Exception as e:
                self.exception_logger(
                    error=f"Selector error [XPATH] failed for selector {named_selector.name} ERROR:{e}",
                    context=ExceptionContext.TRY_FILL_ERROR,
                    failed_selector=named_selector.name,
                )
        if named_selector.selector.css:
            try:
                await page.locator(named_selector.selector.css).fill(*args)
                return
            except Exception as e:
                self.exception_logger(
                    error=f"Selector error [CSS] failed for selector {named_selector.name} ERROR:{e}",
                    context=ExceptionContext.TRY_FILL_ERROR,
                    failed_selector=named_selector.name,
                )
        raise self.exception_logger(
            error=f"all selectors for {named_selector.name} failed",
            context=ExceptionContext.TRY_FILL_ERROR,
            failed_selector=named_selector.name,
        )

    async def try_press_sequential(
        self, page: Page, named_selector: NamedSelector, *args, **kwargs
    ):
        """Generic fill fallback logic."""
        var = {k: kwargs.pop(k) for k in list(kwargs) if k.startswith("var")}

        if var:
            named_selector = self.get_dynamic_selector(named_selector, var)
        if named_selector.selector.xpath:
            try:
                await page.locator(named_selector.selector.xpath).press_sequentially(
                    *args, **kwargs
                )
                return
            except Exception as e:
                self.exception_logger(
                    error=f"Selector error [XPATH] failed for selector {named_selector.name} ERROR:{e}",
                    context=ExceptionContext.TRY_SEQUENTIAL_PRESS_ERROR,
                    failed_selector=named_selector.name,
                )
        if named_selector.selector.css:
            try:
                await page.locator(named_selector.selector.css).press_sequentially(
                    *args, **kwargs
                )
                return
            except Exception as e:
                self.exception_logger(
                    error=f"Selector error [CSS] failed for selector {named_selector.name} ERROR:{e}",
                    context=ExceptionContext.TRY_SEQUENTIAL_PRESS_ERROR,
                    failed_selector=named_selector.name,
                )
        raise self.exception_logger(
            error=f"all selectors for {named_selector.name} failed",
            context=ExceptionContext.TRY_SEQUENTIAL_PRESS_ERROR,
            failed_selector=named_selector.name,
        )

    async def try_press_enter(
        self, page: Page, named_selector: NamedSelector, *args, **kwargs
    ):
        """Generic fill fallback logic."""
        var = {k: kwargs.pop(k) for k in list(kwargs) if k.startswith("var")}

        if var:
            named_selector = self.get_dynamic_selector(named_selector, var)
        if named_selector.selector.xpath:
            try:
                await page.locator(named_selector.selector.xpath).press(*args)
                return
            except Exception as e:
                self.exception_logger(
                    error=f"Selector error [XPATH] failed for selector {named_selector.name} ERROR:{e}",
                    context=ExceptionContext.TRY_PRESS_ENTER_ERROR,
                    failed_selector=named_selector.name,
                )
        if named_selector.selector.css:
            try:
                await page.locator(named_selector.selector.css).press(*args)
                return
            except Exception as e:
                self.exception_logger(
                    error=f"Selector error [CSS] failed for selector {named_selector.name} ERROR:{e}",
                    context=ExceptionContext.TRY_PRESS_ENTER_ERROR,
                    failed_selector=named_selector.name,
                )
        raise self.exception_logger(
            error=f"all selectors for {named_selector.name} failed",
            context=ExceptionContext.TRY_PRESS_ENTER_ERROR,
            failed_selector=named_selector.name,
        )

    async def try_screenshot(self, page: Page, *args):

        try:
            await page.screenshot(path=args[0])
            return
        except Exception as e:
            self.exception_logger(
                error=f"ScreenShot failed  ERROR:{e}",
                context=ExceptionContext.TRY_SCREENSHOT_ERROR,
                failed_selector="screenshot_failed",
            )
        raise self.exception_logger(
            error=f"ScreenShot failed",
            context=ExceptionContext.TRY_SCREENSHOT_ERROR,
            failed_selector="screenshot_failed",
        )

    async def try_wait_for_timeout(self, page: Page, *args):

        try:
            await page.wait_for_timeout(*args)
            return
        except Exception as e:
            self.exception_logger(
                error=f"wait for timeout failed  ERROR:{e}",
                context=ExceptionContext.TRY_WAIT_FOR_TIMEOUT_ERROR,
                failed_selector="wait_for_timeout_Error",
            )
        raise self.exception_logger(
            error=f"wait for timeout failed",
            context=ExceptionContext.TRY_WAIT_FOR_TIMEOUT_ERROR,
            failed_selector="wait_for_timeout_Error",
        )

    async def try_wait_for_load_state(self, page: Page, *args):

        try:
            await page.wait_for_load_state(*args)
            return
        except Exception as e:
            self.exception_logger(
                error=f"wait for timeout failed  ERROR:{e}",
                context=ExceptionContext.TRY_WAIT_FOR_LOAD_STATE_ERROR,
                failed_selector="wait_for_load_state_Error",
            )
        raise self.exception_logger(
            error="wait for timeout failed",
            context=ExceptionContext.TRY_WAIT_FOR_LOAD_STATE_ERROR,
            failed_selector="wait_for_load_state_Error",
        )

    async def try_wait_for(
        self, page: Page, named_selector: NamedSelector, *args, **kwargs
    ):
        var = {k: kwargs.pop(k) for k in list(kwargs) if k.startswith("var")}

        if var:
            named_selector = self.get_dynamic_selector(named_selector, var)
        if named_selector.selector.xpath:
            try:
                await page.locator(named_selector.selector.xpath).wait_for(**kwargs)
                return
            except Exception as e:
                self.exception_logger(
                    error=f"Selector error [XPATH] failed for selector {named_selector.name} ERROR:{e}",
                    context=ExceptionContext.TRY_WAIT_FOR_ERROR,
                    failed_selector=named_selector.name,
                )
        if named_selector.selector.css:
            try:
                await page.locator(named_selector.selector.css).wait_for(**kwargs)
                return
            except Exception as e:
                self.exception_logger(
                    error=f"Selector error [CSS] failed for selector {named_selector.name} ERROR:{e}",
                    context=ExceptionContext.TRY_WAIT_FOR_ERROR,
                    failed_selector=named_selector.name,
                )
        raise self.exception_logger(
            error=f"all selectors for {named_selector.name} failed",
            context=ExceptionContext.TRY_WAIT_FOR_ERROR,
            failed_selector=named_selector.name,
        )

    async def get_innerHtml(
        self, page: Page, named_selector: NamedSelector, *args, **kwargs
    ) -> str:
        var = {k: kwargs.pop(k) for k in list(kwargs) if k.startswith("var")}

        if var:
            named_selector = self.get_dynamic_selector(named_selector, var)
        if named_selector.selector.xpath:
            try:

                return await page.locator(named_selector.selector.xpath).inner_html(
                    **kwargs
                )
            except Exception as e:
                self.exception_logger(
                    error=f"Selector error [XPATH] failed for selector {named_selector.name} ERROR:{e}",
                    context=ExceptionContext.GET_INNER_HTML_ERROR,
                    failed_selector=named_selector.name,
                )
        if named_selector.selector.css:
            try:

                return await page.locator(named_selector.selector.css).inner_html(
                    **kwargs
                )
            except Exception as e:
                self.exception_logger(
                    error=f"Selector error [CSS] failed for selector {named_selector.name} ERROR:{e}",
                    context=ExceptionContext.GET_INNER_HTML_ERROR,
                    failed_selector=named_selector.name,
                )
        raise self.exception_logger(
            error=f"all selectors for {named_selector.name} failed",
            context=ExceptionContext.GET_INNER_HTML_ERROR,
            failed_selector=named_selector.name,
        )

    async def get_page_content(self, page: Page) -> str:

        try:

            return await page.content()
        except Exception as e:

            self.exception_logger(
                error=f"get page content failed  ERROR:{e}",
                context=ExceptionContext.GET_PAGE_CONTENT_ERROR,
                failed_selector="GET_PAGE_CONTENT_ERROR",
            )
        raise self.exception_logger(
            error=f"get page content failed",
            context=ExceptionContext.GET_PAGE_CONTENT_ERROR,
            failed_selector="GET_PAGE_CONTENT_ERROR",
        )

    async def try_scroll_to_page_bottom(self, page: Page, *args, **kwargs):
        try:
            await page.evaluate(
                """
window.scrollTo(0, document.body.scrollHeight);
"""
            )
            return

        except Exception as e:

            self.exception_logger(
                error=f"scroll To Page Bottom  failed  ERROR:{e}",
                context=ExceptionContext.TRY_SCROLL_TO_PAGE_BOTTOM_ERROR,
                failed_selector="SCROLL_TO_PAGE_BOTTOM_ERROR",
            )
        raise self.exception_logger(
            error=f"scroll To Page Bottom  failed failed",
            context=ExceptionContext.TRY_SCROLL_TO_PAGE_BOTTOM_ERROR,
            failed_selector="SCROLL_TO_PAGE_BOTTOM_ERROR",
        )
