# import sys
# import asyncio

# if sys.platform == "win32":
#     asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

from typing import Union

from fastapi import FastAPI
from app.core.scraper_engine import ScraperEngine
app = FastAPI()


@app.get("/")
async def read_root():
    obj=ScraperEngine(url="https://www.google.com")
    await obj.runScraper()
    return {"success"}


@app.get("/items/{item_id}")
def read_item(item_id: int, q: Union[str, None] = None):        
    return {"item_id": item_id, "q": q}