# import sys
# import asyncio

# if sys.platform == "win32":
#     asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

from typing import Union
from app.config_loader import scraper_config_loader
from fastapi import FastAPI
from app.core.scrapers.select_scraper import scrapers

app = FastAPI()

config = scraper_config_loader


@app.get("/")
async def read_root():

    results = {}
    for airline_name, scraper_config in scraper_config_loader.items():
        try:
            scraper_select = scrapers.get(airline_name.lower())
            if not scraper_select:
                print(f"No scraper found for {airline_name}")
                continue

            from_and_to = scraper_config.from_and_to_city

            for from_city, to_city_list in from_and_to.items():
                for to_city in to_city_list:
                    scraper = scraper_select(
                        url=scraper_config.url,
                        airline_name=airline_name,  # using key from loader
                        fingerprint_spoof=scraper_config.fingerprint_spoof,
                        from_city=from_city,
                        to_city=to_city,
                        trip_type=scraper_config.trip_type,
                    )
                    await scraper.run_scrapper()
                    results[airline_name] = "success"
        except Exception as e:
            results[airline_name] = f"failed: {str(e)}"

    return results


@app.get("/items/{item_id}")
def read_item(item_id: int, q: Union[str, None] = None):
    return {"item_id": item_id, "q": q}
