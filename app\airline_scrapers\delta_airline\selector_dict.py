from app.core.types.selector_types import SelectorPair
from app.core.utils.selector_type_util import make_selectors

SELECTORS = make_selectors(
    {
        # initial page
        "accept_cookies": SelectorPair(
            xpath='//*[@id="onetrust-reject-all-handler"]',
            css="#onetrust-close-btn-container",
        ),
        "select_trip_type_dropdown": SelectorPair(
            xpath='//*[@id="booking"]/form/div[1]/div/div[1]/div[1]/div[2]/span/span[1]',
            css="#selectTripType-val",
        ),
        "select_one_way_trip": SelectorPair(
            xpath='//*[@id="ui-list-selectTripType1"]', css="#ui-list-selectTripType1"
        ),
        "click_shop_with_miles": SelectorPair(
            xpath='//*[@id="booking"]/form/div[1]/div/div[1]/div[2]/ngc-search-options/fieldset/div/div[2]',
            css="#shopWithMiles",
        ),
        "select_departure_date": SelectorPair(
            xpath='//*[@id="input_departureDate_1"]', css="#input_departureDate_1"
        ),
        "select_departure_date_day": SelectorPair(
            xpath='//*[@id="booking"]/form/div[1]/div/div[1]/div[1]/div[3]/date-selection-view/div/div/div/div/div[4]/div/div[2]/div[1]/div[2]/table/tbody/tr[4]/td[3]/a',
            css="#booking > form > div.container.booking-widget_container-mobile.ng-tns-c88-2.book-container-padding-bottom > div > div.col-lg-10.pl-xl-0.pl-xxl-0.p-0.pt-sm-3.safari-mob-padding.ng-tns-c88-2 > div:nth-child(1) > div.col-lg-3.col-sm-12.d-lg-block.offset-md-2.col-md-8.offset-lg-0.book-element.ng-tns-c88-2.d-sm-none.booking-element.ng-star-inserted > date-selection-view > div > div > div > div > div.calenderContainer > div > div.dl-datepicker-group-wrapper > div.dl-datepicker-group.dl-datepicker-group-0 > div.dl-datepicker-calendar-cont > table > tbody > tr:nth-child(4) > td.dl-datepicker-available-day.days-between.days-between-start > a",
        ),
        "click_done_button": SelectorPair(
            xpath='//*[@id="booking"]/form/div[1]/div/div[1]/div[1]/div[3]/date-selection-view/div/div/div/div/div[4]/div/div[3]/button[2]',
            css='[aria-label="done"]',
        ),
        "from_airport": SelectorPair(
            xpath='//*[@id="fromAirportName"]', css="#fromAirportName"
        ),
        "to_airport": SelectorPair(
            xpath='//*[@id="toAirportName"]', css="#toAirportName"
        ),
        "search_input": SelectorPair(
            xpath='//*[@id="search_input"]', css="#search_input"
        ),
        "initial_submit_button": SelectorPair(
            xpath='//*[@id="btnSubmit"]', css="#btnSubmit"
        ),
        "flexible_date_result_page": SelectorPair(
            xpath="/html/body/idp-root/div/div[2]/idp-flexible-dates/div/div[2]/div[3]/idp-flexible-dates-footer-template/div/idp-button/button",
            css="body > idp-root > div > div.idp-shopping-slice__body > idp-flexible-dates > div > div.container > div.flexible-dates-page__footer > idp-flexible-dates-footer-template > div > idp-button > button",
        ),
        "flight_details": SelectorPair(
            xpath='//*[@id="#details-link{var0}"]', css="#\\#details-link{var0}"
        ),
        "seat_details": SelectorPair(
            xpath='//*[@id="#seats-link{var0}"]', css="#\\#seats-link{var0}"
        ),
        "wait_for_flight_details_content": SelectorPair(
            xpath='//*[@id="headingOne1"]/div[1]/div',
            css="#headingOne1 > div.accordion-header-head-info > div",
        ),
        "details_close_button": SelectorPair(
            xpath="/html/body/idp-root/div/div[2]/idp-search-results/div/div[5]/idp-flight-grid/div[10]/idp-simple-modal/div/div[1]/div/div[1]/button/i",
            css="body > idp-root > div > div.idp-shopping-slice__body > idp-search-results > div > div.search-results__grid.container-lg-up.container.container-lg-down > idp-flight-grid > div.flight-specific-modal-header.ng-star-inserted > idp-simple-modal > div > div.idp-dialog.secondary > div > div.idp-dialog__header.header--dark.closeOnBackgroundClick.ng-star-inserted > button > i",
        ),
        "seat_details_close_button": SelectorPair(
            xpath="/html/body/idp-root/div/div[2]/idp-search-results/div/div[5]/idp-flight-grid/div[10]/idp-logical-seat-map/div/div[1]/idp-flight-header-container/div/div/idp-flight-list-header/div/div/div[5]/button",
            css="body > idp-root > div > div.idp-shopping-slice__body > idp-search-results > div > div.search-results__grid.container-lg-up.container.container-lg-down > idp-flight-grid > div.logical-seat-map.ng-star-inserted > idp-logical-seat-map > div > div:nth-child(1) > idp-flight-header-container > div > div > idp-flight-list-header > div > div > div.idp-flight__exit-seatmap.idp-flight--display-properties > button",
        ),
        "click_main_cabinClass": SelectorPair(
            xpath='//*[@id="button0"]/div[2]/div',
            css="#button0 > div.brand-tabs__info-box.ng-star-inserted > div",
        ),
        "click_comfort_cabinClass": SelectorPair(
            xpath='//*[@id="button1"]/div[2]/div',
            css="#button1 > div.brand-tabs__info-box.ng-star-inserted > div",
        ),
        "click_first_cabinClass": SelectorPair(
            xpath='//*[@id="button2"]/div[2]/div',
            css="#button2 > div.brand-tabs__info-box.ng-star-inserted > div",
        ),
        "flight_details_dom_selector": SelectorPair(
            xpath="/html/body/idp-root/div/div[2]/idp-search-results/div/div[5]/idp-flight-grid/div[10]/idp-simple-modal/div/div[1]/div/div[2]",
            css="body > idp-root > div > div.idp-shopping-slice__body > idp-search-results > div > div.search-results__grid.container-lg-up.container.container-lg-down > idp-flight-grid > div.flight-specific-modal-header.ng-star-inserted > idp-simple-modal > div > div.idp-dialog.secondary > div > div.idp-dialog__content",
        ),
        "seat_details_load": SelectorPair(
            xpath='//*[@id="brand0"]/aura-expansion-panel/div[2]/div/article',
            css="#brand0 > aura-expansion-panel > div.aura-expansion-panel__body > div > article",
        ),
        "see_more_result_button": SelectorPair(
            xpath="/html/body/idp-root/div/div[2]/idp-search-results/div/div[6]/div/div/button",
            css="body > idp-root > div > div.idp-shopping-slice__body > idp-search-results > div > div.search-results__mach-load-more-btn.ng-star-inserted > div > div > button",
            getByText=" See More Results ",
        ),
    }
)
