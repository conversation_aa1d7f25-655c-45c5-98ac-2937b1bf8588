from app.core.exception.exception_logger import ExceptionLogger
from app.core.types.exception_types import ExceptionContext
from app.utils.fallback_map_helper import RegisterFallbackHelper


class DeltaFallback(RegisterFallbackHelper):

    def __init__(self):
        super().__init__()
        self.predefined_fallback: list[tuple[ExceptionContext, str, callable]] = [
            (ExceptionContext.TRY_CLICK_ERROR, "accept_cookies", self.kill_and_restart),
            (
                ExceptionContext.TRY_CLICK_ERROR,
                "seat_details_close_button",
                self.kill_and_restart,
            ),
        ]

        self.register_handlers()

    def register_handlers(self):

        for context, selector_name, handler in self.predefined_fallback:
            self._register_fallback(context, selector_name, handler)

    async def delta_fallback_handler(
        self, page, context: ExceptionContext, failed_selector: str
    ):

        try:
            handler = self.fallback_map.get(context, {}).get(failed_selector)
            if handler:
                await handler(page)
            else:
                raise Exception(
                    f"No fallback handler for context={context}, selector='{failed_selector}'"
                )
        except Exception as e:
            print(f"Fallback error: {e}")

    async def kill_and_restart(self, page):
        print("Killing and restarting browser...")
        await page.context.close()
