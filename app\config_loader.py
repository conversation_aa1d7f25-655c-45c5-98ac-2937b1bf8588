from typing import Dict

from pydantic import ValidationError

from app.config.scraper_config.delta_airline_config import scraper_config as delta
from app.config.scraper_schema import ScraperSettings

try:
    scraper_config_loader: Dict[str, ScraperSettings] = {
        "delta": ScraperSettings(**delta)  # type: ignore
    }
except ValidationError as e:
    print("Validation error in delta config:", e)
except Exception as e:
    print("Unexpected error loading delta config:", e)
