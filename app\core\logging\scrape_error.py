import os
import logging

os.makedirs("logs", exist_ok=True)

# Create a named logger for scraper errors
scraper_logger = logging.getLogger("scraper_errors")
scraper_logger.setLevel(logging.ERROR)

# Avoid duplicate logs if the handler already exists
if not scraper_logger.handlers:
    file_handler = logging.FileHandler("logs/scraper_errors.log")
    formatter = logging.Formatter(
        "%(asctime)s | %(levelname)s | %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    file_handler.setFormatter(formatter)
    scraper_logger.addHandler(file_handler)
