from app.core.types.exception_types import ExceptionContext


class ScraperException(Exception):
    def __init__(
        self,
        message: str,
        context: ExceptionContext = ExceptionContext.VALIDATION_ERROR,
        failed_selector: str = "",
    ):
        self.message = message
        self.context = context
        self.failed_selector = failed_selector
        self.type = context.value
        custom_format = f"[{context}] {message} failed_selector: {failed_selector}"
        print(f"[EXCEPTION] {custom_format}")
        super().__init__(custom_format)
