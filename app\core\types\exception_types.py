from enum import Enum


class ExceptionContext(Enum):
    VALIDATION_ERROR = "VALIDATION_ERROR"
    TIMEOUT_ERROR = "TIMEOUT_ERROR"
    TRY_CLICK_ERROR = "TRY_CLICK_ERROR"
    SELECTOR_NOT_FOUND = "SELECTOR_NOT_FOUND"
    BOT_DETECTED_ERROR = "BOT_DETECTED_ERROR"
    TRY_CHECK_VISIBILITY_ERROR = "TRY_CHECK_VISIBILITY_ERROR"
    DYNAMIC_SELECTOR_ERROR = "DYNAMIC_SELECTOR_ERROR"
    TRY_FILL_ERROR = "TRY_FILL_ERROR"
    TRY_SEQUENTIAL_PRESS_ERROR = "TRY_SEQUENTIAL_PRESS_ERROR"
    TRY_PRESS_ENTER_ERROR = "TRY_PRESS_ENTER_ERROR"
    TRY_SCREENSHOT_ERROR = "TRY_SCREENSHOT_ERROR"
    TRY_WAIT_FOR_TIMEOUT_ERROR = "TRY_WAIT_FOR_TIMEOUT_ERROR"
    TRY_WAIT_FOR_LOAD_STATE_ERROR = "TRY_WAIT_FOR_LOAD_STATE_ERROR"
    TRY_WAIT_FOR_ERROR = "TRY_WAIT_FOR_ERROR"
    GET_INNER_HTML_ERROR = "GET_INNER_HTML_ERROR"
    GET_PAGE_CONTENT_ERROR = "GET_PAGE_CONTENT_ERROR"
    TRY_SCROLL_TO_PAGE_BOTTOM_ERROR = "TRY_SCROLL_TO_PAGE_BOTTOM_ERROR"
    DELTA_SCRAPER_FATAL_ERROR = "DELTA_SCRAPER_FATAL_ERROR"
