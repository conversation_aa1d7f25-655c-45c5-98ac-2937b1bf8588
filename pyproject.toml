[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "venv",
    ".venv",
    "build",
    "dist",
    "*.egg-info"
]

[tool.ruff]
line-length = 88
target-version = "py39"
exclude = [
    ".git",
    "__pycache__",
    "venv",
    ".venv",
    "build",
    "dist",
    "*.egg-info"
]

[tool.ruff.lint]
select = ["E", "F", "W", "I"]
ignore = ["E203", "W503", "E501"]
