from browserforge.headers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from browserforge.fingerprints import FingerprintGenerator,Screen,ScreenFingerprint
# from browserforge.fingerprints import 
from browserforge.injectors.playwright import As<PERSON><PERSON><PERSON><PERSON>ontext
from playwright.async_api import <PERSON><PERSON>er
async def spoofGenerator(browser: Browser):
    
    
    
    
    screen = Screen(
               min_width=1280,
               max_width=1600,
               min_height=600,
               max_height=900
               
               
             )
    fingerprints = FingerprintGenerator(screen=screen , )
    fingerprint= fingerprints.generate(browser='chrome', os='windows')
    if fingerprint.screen.innerWidth < 1000:
         fingerprint.screen.innerWidth = 1280
    if fingerprint.screen.innerHeight < 600:
        fingerprint.screen.innerHeight = 720
    if fingerprint.screen.clientWidth < 1000:
        fingerprint.screen.clientWidth = fingerprint.screen.innerWidth - 20
    if fingerprint.screen.clientHeight < 1000:
        fingerprint.screen.clientHeight  = fingerprint.screen.innerHeight  - 20
    headers= HeaderGenerator()
    
    resultant_header=headers.generate(browser='chrome' , os='windows')
    print(resultant_header)

    context = await As<PERSON><PERSON><PERSON><PERSON>ontext(
    browser=browser,
    fingerprint=fingerprint,
    user_agent=resultant_header["User-Agent"],
    extra_http_headers=resultant_header
)
    return context
    

    