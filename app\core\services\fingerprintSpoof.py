from browserforge.headers import <PERSON><PERSON><PERSON>ener<PERSON>
from browserforge.fingerprints import FingerprintGenerator,Screen,ScreenFingerprint
# from browserforge.fingerprints import 
from browserforge.injectors.playwright import AsyncN<PERSON><PERSON>ontext

async def spoofGenerator(browser):
    
    
    
    
    screen = Screen(
               min_width=1280,
               max_width=1600,
               min_height=600,
               max_height=900
               
               
             )
    fingerprints = FingerprintGenerator(screen=screen , )
    fingerprint= fingerprints.generate(browser='chrome', os='windows')
    if fingerprint.screen.innerWidth < 1000:
         fingerprint.screen.innerWidth = 1280
    if fingerprint.screen.innerHeight < 600:
        fingerprint.screen.innerHeight = 720
    if fingerprint.screen.clientWidth < 1000:
        fingerprint.screen.clientWidth = fingerprint.screen.innerWidth - 20
    if fingerprint.screen.clientHeight < 1000:
        fingerprint.screen.clientHeight  = fingerprint.screen.innerHeight  - 20
    headers= HeaderGenerator()
    
    resultant_header=headers.generate(browser=fingerprint.headers.browser , os=fingerprint.headers.os)
    context = await Async<PERSON>ew<PERSON>ontext(
    browser=browser,
    fingerprint=fingerprint,
    user_agent=resultant_header["user-agent"],
    extra_http_headers=resultant_header
)
    return context
    

    