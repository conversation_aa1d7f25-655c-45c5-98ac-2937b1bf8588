import asyncio
from typing import Union
from abc import ABC, abstractmethod
from app.core.fallback_generic.selector_fallback import Fallback
from datetime import datetime
from playwright.async_api import async_playwright
from app.core.services import fingerprint_spoof as spoof


class ScraperEngine(ABC, Fallback):

    def __init__(
        self,
        url: str,
        airline_name: str,
        to_city: str,
        from_city: str,
        trip_type: str,
        fingerprint_spoof: bool = False,
        proxy_change: bool = False,
        ghost_mouse: bool = False,
    ):
        self.url = url
        self.name = airline_name
        self.fingerprint_spoof = fingerprint_spoof
        self.from_city = from_city
        self.to_city = to_city
        self.trip_type = trip_type
        super().__init__()

    async def run_scrapper(self):
        # from playwright_stealth import Stealth

        # from app.core.scrapers import base_scraper
        # return {"hello"}

        async with async_playwright() as p:
            # async with Stealth().use_async(async_playwright()) as p:
            # service for proxy
            browser = await p.chromium.launch(headless=False, slow_mo=1000)
            context = await browser.new_context()
            flag = False
            # page = await context.new_page()
            if self.fingerprint_spoof:
                flag = True
                spoofed_context = await spoof.spoof_generator(browser)
                page = await spoofed_context.new_page()
            else:
                page = await context.new_page()

            await self._scrape(page)
            await page.wait_for_timeout(1000)
            await browser.close()
        return {"status": "scraping done"}

    @abstractmethod
    async def _scrape(self, page):
        pass
