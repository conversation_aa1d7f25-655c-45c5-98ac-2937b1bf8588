import asyncio
from typing import Union

# --- Fix for <PERSON><PERSON> on Windows with asyncio ---
# This MUST be at the top of the file, before FastAPI and other async libraries are imported.
# import sys
# if sys.platform == "win32":
#     asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

class ScraperEngine:
    # def __init__(self, url: str, arilineName: str, from_city: str , to_city: str, date: str, return_date: str, one_way_trip: bool, fingerprintSpoof: bool  ):
    #     self.url = url
    #     self.airlineName = arilineName  
    #     self.from_city = from_city
    #     self.to_city = to_city
    #     self.date = date
    #     self.return_date = return_date
    #     self.one_way_trip = one_way_trip
       

    def __init__(self, url: str , name: str):
        self.url = url
        self.name = name

    async def runScraper(self):
        await self._scrape()
    async def _scrape(self): 
        
        from playwright_stealth import Stealth
        from playwright.async_api import async_playwright
        from app.core.services import fingerprintSpoof as spoof
        from app.core.scrapers import base_scraper
        # return {"hello"}
       
            
            
        async with <PERSON>ealth().use_async(async_playwright()) as p:
            # service for proxy
            browser = await p.chromium.launch(headless=False)
            context = await browser.new_context()
            page = await context.new_page()
            if(self.fingerprintSpoof):
                    spoofed_context=await spoof.spoofGenerator(browser)
                    page = await spoofed_context.new_page()
            else:
                    page = await context.new_page()
                
            
            await page.goto(self.url)
            await base_scraper.selectScraper(self.name , page)
                # await page.wait_for_load_state('networkidle')
            await page.wait_for_timeout(1000)
            await browser.close()
        return {"status": "scraping done"}
       
            