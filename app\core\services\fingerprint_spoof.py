from browserforge.headers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from browserforge.fingerprints import FingerprintGenerator, Screen

# from browserforge.fingerprints import
from browserforge.injectors.playwright import As<PERSON><PERSON>ew<PERSON>ontext
from playwright.async_api import <PERSON>rowser

async def spoof_generator(browser: Browser):

    screen = Screen(min_width=1280, max_width=1600,
    min_height=600, max_height=900)
    fingerprints = FingerprintGenerator(
        screen=screen,
    )
    fingerprint = fingerprints.generate(browser="chrome", os="windows")
    if fingerprint.screen.innerWidth < 1000:
        fingerprint.screen.innerWidth = 1280
    if fingerprint.screen.innerHeight < 600:
        fingerprint.screen.innerHeight = 720
    if fingerprint.screen.clientWidth < 1000:
        fingerprint.screen.clientWidth = fingerprint.screen.innerWidth - 20
    if fingerprint.screen.clientHeight < 1000:
        fingerprint.screen.clientHeight = fingerprint.screen.innerHeight - 20
    headers = HeaderGenerator()

    resultant_header = headers.generate(browser="chrome", os="windows")
    print(resultant_header)

    fingerprint.pluginsData = {
        "plugins": [
            {
                "name": "Chrome PDF Viewer",
                "description": "Portable Document Format",
                "filename": "internal-pdf-viewer",
                "mimeTypes": [
                    {
                        "type": "application/pdf",
                        "suffixes": "pdf",
                        "description": "Portable Document Format",
                        "enabledPlugin": "Chrome PDF Viewer",
                    }
                ],
            }
        ],
        "mimeTypes": ["Portable Document Format~~application/pdf~~pdf"],
    }
    fingerprint.navigator.userAgentData["platformVersion"] = "10.0.0"
    worker_thread = fingerprint.navigator.hardwareConcurrency
    print(f"fingerprint thread {worker_thread}")
    # nned to copy the workerintercept to a temp file and then make replacement
    with open(
        "app/assets/fingerprint_assets/worker_intercept.js", "r", encoding="utf-8"
    ) as f:
        custom_script_file = f.read()

        # Step 2: Replace placeholder with dynamic value

        worker_intercept = custom_script_file.replace(
            "_worker_thread", f"{worker_thread}"
        )

        # Step 3: Write modified content to new or same file
        # with open("app/assets/fingerprintAssets/worker_intercept.js", "w", encoding="utf-8") as f:
        #  f.write(modified_content)
        print("File updated successfully.")
    context = await AsyncNewContext(
        browser=browser,
        fingerprint=fingerprint,
        user_agent=resultant_header["User-Agent"],
        extra_http_headers=resultant_header,
    )
    # script_path = Path(__file__).resolve().parent.parent.parent / "assets" / "fingerprintAssets" / "worker_intercept.js"
    await context.add_init_script(f"{worker_intercept}")

    return context
